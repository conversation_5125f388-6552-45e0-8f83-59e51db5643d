// Test script to verify the transaction fix
const { ConsolidatedTransactionService } = require('./lib/consolidated-transaction-service.ts');
const { PaymentMethod } = require('./lib/transaction-types.ts');

// Mock appointment data
const testAppointment = {
  id: 'apt-test-001',
  clientId: 'client-001',
  clientName: 'Test Client',
  staffId: 'staff-001',
  staffName: 'Test Staff',
  service: 'Haircut',
  price: 100,
  additionalServices: [
    { name: 'Hair Wash', price: 25 },
    { name: 'Hair Styling', price: 50 }
  ],
  products: [
    { id: 'prod-001', name: 'Shampoo', price: 30 },
    { id: 'prod-002', name: 'Conditioner', price: 50 }
  ],
  location: 'loc1',
  date: new Date().toISOString()
};

console.log('🧪 Testing Consolidated Transaction Service');
console.log('==========================================');

// Test 1: Create transaction without discount
console.log('\n📝 Test 1: Transaction without discount');
try {
  const transaction1 = ConsolidatedTransactionService.createConsolidatedTransaction(
    testAppointment,
    PaymentMethod.CASH,
    0,
    0
  );
  
  console.log('✅ Transaction created successfully');
  console.log('Transaction ID:', transaction1.id);
  console.log('Original Amount:', transaction1.originalAmount);
  console.log('Final Amount (amount):', transaction1.amount);
  console.log('Service Amount:', transaction1.serviceAmount);
  console.log('Product Amount:', transaction1.productAmount);
  console.log('Original Service Amount:', transaction1.originalServiceAmount);
  console.log('Discount Amount:', transaction1.discountAmount);
  
  // Verify calculations
  const expectedOriginalServiceAmount = 100 + 25 + 50; // 175
  const expectedProductAmount = 30 + 50; // 80
  const expectedOriginalAmount = expectedOriginalServiceAmount + expectedProductAmount; // 255
  
  console.log('\n🔍 Verification:');
  console.log('Expected Original Service Amount:', expectedOriginalServiceAmount);
  console.log('Actual Original Service Amount:', transaction1.originalServiceAmount);
  console.log('Expected Product Amount:', expectedProductAmount);
  console.log('Actual Product Amount:', transaction1.productAmount);
  console.log('Expected Original Amount:', expectedOriginalAmount);
  console.log('Actual Original Amount:', transaction1.originalAmount);
  
  if (transaction1.originalAmount === expectedOriginalAmount &&
      transaction1.originalServiceAmount === expectedOriginalServiceAmount &&
      transaction1.productAmount === expectedProductAmount &&
      transaction1.amount === expectedOriginalAmount) {
    console.log('✅ Test 1 PASSED - All amounts calculated correctly');
  } else {
    console.log('❌ Test 1 FAILED - Amount calculations incorrect');
  }
} catch (error) {
  console.log('❌ Test 1 FAILED:', error.message);
}

// Test 2: Create transaction with 20% discount
console.log('\n📝 Test 2: Transaction with 20% discount');
try {
  const discountPercentage = 20;
  const transaction2 = ConsolidatedTransactionService.createConsolidatedTransaction(
    testAppointment,
    PaymentMethod.CREDIT_CARD,
    discountPercentage,
    35 // 20% of 175 = 35
  );
  
  console.log('✅ Transaction created successfully');
  console.log('Transaction ID:', transaction2.id);
  console.log('Original Amount:', transaction2.originalAmount);
  console.log('Final Amount (amount):', transaction2.amount);
  console.log('Service Amount:', transaction2.serviceAmount);
  console.log('Product Amount:', transaction2.productAmount);
  console.log('Original Service Amount:', transaction2.originalServiceAmount);
  console.log('Discount Amount:', transaction2.discountAmount);
  console.log('Discount Percentage:', transaction2.discountPercentage);
  
  // Verify calculations
  const expectedOriginalServiceAmount = 100 + 25 + 50; // 175
  const expectedDiscountedServiceAmount = expectedOriginalServiceAmount * 0.8; // 140
  const expectedProductAmount = 30 + 50; // 80 (no discount on products)
  const expectedOriginalAmount = expectedOriginalServiceAmount + expectedProductAmount; // 255
  const expectedFinalAmount = expectedDiscountedServiceAmount + expectedProductAmount; // 220
  const expectedDiscountAmount = expectedOriginalServiceAmount - expectedDiscountedServiceAmount; // 35
  
  console.log('\n🔍 Verification:');
  console.log('Expected Original Service Amount:', expectedOriginalServiceAmount);
  console.log('Actual Original Service Amount:', transaction2.originalServiceAmount);
  console.log('Expected Discounted Service Amount:', expectedDiscountedServiceAmount);
  console.log('Actual Service Amount:', transaction2.serviceAmount);
  console.log('Expected Product Amount:', expectedProductAmount);
  console.log('Actual Product Amount:', transaction2.productAmount);
  console.log('Expected Original Amount:', expectedOriginalAmount);
  console.log('Actual Original Amount:', transaction2.originalAmount);
  console.log('Expected Final Amount:', expectedFinalAmount);
  console.log('Actual Final Amount:', transaction2.amount);
  console.log('Expected Discount Amount:', expectedDiscountAmount);
  console.log('Actual Discount Amount:', transaction2.discountAmount);
  
  if (Math.abs(transaction2.originalAmount - expectedOriginalAmount) < 0.01 &&
      Math.abs(transaction2.originalServiceAmount - expectedOriginalServiceAmount) < 0.01 &&
      Math.abs(transaction2.serviceAmount - expectedDiscountedServiceAmount) < 0.01 &&
      Math.abs(transaction2.productAmount - expectedProductAmount) < 0.01 &&
      Math.abs(transaction2.amount - expectedFinalAmount) < 0.01 &&
      Math.abs(transaction2.discountAmount - expectedDiscountAmount) < 0.01) {
    console.log('✅ Test 2 PASSED - All amounts calculated correctly with discount');
  } else {
    console.log('❌ Test 2 FAILED - Amount calculations incorrect with discount');
  }
} catch (error) {
  console.log('❌ Test 2 FAILED:', error.message);
}

console.log('\n🏁 Testing completed');
